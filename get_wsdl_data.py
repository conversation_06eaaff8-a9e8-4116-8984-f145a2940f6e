#!/usr/bin/env python3
"""
简单的WSDL数据获取脚本
"""

import requests
import xml.etree.ElementTree as ET

def get_wsdl_data():
    """从WSDL服务获取数据"""
    
    # WSDL服务URL
    url = "http://10.60.26.10:9090/webservice.asmx?WSDL"
    
    try:
        print(f"正在获取WSDL数据: {url}")
        
        # 发送GET请求获取WSDL文档
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        print("WSDL数据获取成功!")
        print(f"响应状态码: {response.status_code}")
        print(f"内容类型: {response.headers.get('content-type', 'unknown')}")
        print(f"内容长度: {len(response.text)} 字符")
        
        # 保存原始WSDL文档
        with open('wsdl_document.xml', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("WSDL文档已保存到: wsdl_document.xml")
        
        # 解析WSDL文档，提取服务信息
        try:
            root = ET.fromstring(response.text)
            
            # 查找服务名称
            services = []
            for elem in root.iter():
                if 'service' in elem.tag.lower():
                    name = elem.get('name')
                    if name:
                        services.append(name)
            
            # 查找操作/方法
            operations = []
            for elem in root.iter():
                if 'operation' in elem.tag.lower():
                    name = elem.get('name')
                    if name:
                        operations.append(name)
            
            print(f"\n发现的服务: {services}")
            print(f"发现的操作: {operations}")
            
        except ET.ParseError as e:
            print(f"解析XML时出错: {e}")
        
        return response.text
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def call_soap_method(method_name, parameters=None):
    """调用SOAP方法"""
    
    if parameters is None:
        parameters = {}
    
    # 构建SOAP请求体
    param_xml = ""
    for key, value in parameters.items():
        param_xml += f"<{key}>{value}</{key}>"
    
    soap_body = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
               xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <{method_name} xmlns="http://tempuri.org/">
      {param_xml}
    </{method_name}>
  </soap:Body>
</soap:Envelope>"""
    
    headers = {
        'Content-Type': 'text/xml; charset=utf-8',
        'SOAPAction': f'http://tempuri.org/{method_name}'
    }
    
    service_url = "http://10.60.26.10:9090/webservice.asmx"
    
    try:
        print(f"正在调用方法: {method_name}")
        response = requests.post(service_url, data=soap_body, headers=headers, timeout=30)
        response.raise_for_status()
        
        print("方法调用成功!")
        print(f"响应: {response.text}")
        
        return response.text
        
    except requests.exceptions.RequestException as e:
        print(f"调用方法失败: {e}")
        return None

if __name__ == "__main__":
    # 获取WSDL数据
    wsdl_data = get_wsdl_data()
    
    if wsdl_data:
        print("\n" + "="*50)
        print("WSDL数据获取完成!")
        
        # 示例：如果你知道具体的方法名，可以这样调用
        # result = call_soap_method("YourMethodName", {"param1": "value1"})
        
        print("\n如果需要调用具体的SOAP方法，请修改脚本中的方法名和参数")
