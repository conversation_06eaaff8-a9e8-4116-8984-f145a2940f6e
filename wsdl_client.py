#!/usr/bin/env python3
"""
WSDL Web Service Client
用于从WSDL服务获取数据的Python客户端
"""

import requests
import xml.etree.ElementTree as ET
from urllib.parse import urljoin
import json
import sys

class WSDLClient:
    def __init__(self, wsdl_url):
        """
        初始化WSDL客户端
        
        Args:
            wsdl_url (str): WSDL服务的URL
        """
        self.wsdl_url = wsdl_url
        self.base_url = wsdl_url.replace('?WSDL', '')
        self.session = requests.Session()
        self.wsdl_content = None
        self.operations = {}
        
    def fetch_wsdl(self):
        """获取WSDL文档"""
        try:
            print(f"正在获取WSDL文档: {self.wsdl_url}")
            response = self.session.get(self.wsdl_url, timeout=30)
            response.raise_for_status()
            self.wsdl_content = response.text
            print("WSDL文档获取成功")
            return True
        except requests.exceptions.RequestException as e:
            print(f"获取WSDL文档失败: {e}")
            return False
    
    def parse_wsdl(self):
        """解析WSDL文档，提取可用的操作"""
        if not self.wsdl_content:
            print("请先获取WSDL文档")
            return False
            
        try:
            root = ET.fromstring(self.wsdl_content)
            
            # 定义命名空间
            namespaces = {
                'wsdl': 'http://schemas.xmlsoap.org/wsdl/',
                'soap': 'http://schemas.xmlsoap.org/wsdl/soap/',
                'tns': 'http://tempuri.org/'
            }
            
            # 查找所有操作
            operations = root.findall('.//wsdl:operation', namespaces)
            
            for op in operations:
                op_name = op.get('name')
                if op_name:
                    self.operations[op_name] = {
                        'name': op_name,
                        'input': None,
                        'output': None
                    }
                    
                    # 查找输入和输出消息
                    input_elem = op.find('wsdl:input', namespaces)
                    output_elem = op.find('wsdl:output', namespaces)
                    
                    if input_elem is not None:
                        self.operations[op_name]['input'] = input_elem.get('message', '').replace('tns:', '')
                    
                    if output_elem is not None:
                        self.operations[op_name]['output'] = output_elem.get('message', '').replace('tns:', '')
            
            print(f"发现 {len(self.operations)} 个操作:")
            for op_name in self.operations:
                print(f"  - {op_name}")
            
            return True
            
        except ET.ParseError as e:
            print(f"解析WSDL文档失败: {e}")
            return False
    
    def call_operation(self, operation_name, parameters=None):
        """
        调用SOAP操作
        
        Args:
            operation_name (str): 操作名称
            parameters (dict): 参数字典
        """
        if operation_name not in self.operations:
            print(f"操作 '{operation_name}' 不存在")
            return None
        
        if parameters is None:
            parameters = {}
        
        # 构建SOAP请求
        soap_envelope = self._build_soap_request(operation_name, parameters)
        
        headers = {
            'Content-Type': 'text/xml; charset=utf-8',
            'SOAPAction': f'http://tempuri.org/{operation_name}'
        }
        
        try:
            print(f"正在调用操作: {operation_name}")
            response = self.session.post(
                self.base_url,
                data=soap_envelope,
                headers=headers,
                timeout=30
            )
            response.raise_for_status()
            
            print("操作调用成功")
            return self._parse_soap_response(response.text)
            
        except requests.exceptions.RequestException as e:
            print(f"调用操作失败: {e}")
            return None
    
    def _build_soap_request(self, operation_name, parameters):
        """构建SOAP请求"""
        param_xml = ""
        for key, value in parameters.items():
            param_xml += f"<{key}>{value}</{key}>"
        
        soap_envelope = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
               xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
               xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <{operation_name} xmlns="http://tempuri.org/">
      {param_xml}
    </{operation_name}>
  </soap:Body>
</soap:Envelope>"""
        
        return soap_envelope
    
    def _parse_soap_response(self, response_text):
        """解析SOAP响应"""
        try:
            root = ET.fromstring(response_text)
            
            # 移除命名空间前缀以简化处理
            for elem in root.iter():
                if '}' in elem.tag:
                    elem.tag = elem.tag.split('}')[1]
            
            # 查找Body中的内容
            body = root.find('.//Body')
            if body is not None:
                # 返回Body的第一个子元素
                for child in body:
                    return self._element_to_dict(child)
            
            return response_text
            
        except ET.ParseError:
            return response_text
    
    def _element_to_dict(self, element):
        """将XML元素转换为字典"""
        result = {}
        
        # 处理元素文本
        if element.text and element.text.strip():
            if len(element) == 0:
                return element.text.strip()
            else:
                result['text'] = element.text.strip()
        
        # 处理子元素
        for child in element:
            child_data = self._element_to_dict(child)
            if child.tag in result:
                # 如果已存在同名元素，转换为列表
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        # 处理属性
        if element.attrib:
            result['@attributes'] = element.attrib
        
        return result if result else None
    
    def get_operations(self):
        """获取所有可用操作的列表"""
        return list(self.operations.keys())
    
    def save_wsdl(self, filename="wsdl_document.xml"):
        """保存WSDL文档到文件"""
        if self.wsdl_content:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.wsdl_content)
            print(f"WSDL文档已保存到: {filename}")
        else:
            print("没有WSDL内容可保存")


def main():
    """主函数 - 演示如何使用WSDLClient"""
    
    # WSDL服务URL
    wsdl_url = "http://10.60.26.10:9090/webservice.asmx?WSDL"
    
    # 创建客户端
    client = WSDLClient(wsdl_url)
    
    # 获取并解析WSDL
    if not client.fetch_wsdl():
        print("无法获取WSDL文档，程序退出")
        sys.exit(1)
    
    if not client.parse_wsdl():
        print("无法解析WSDL文档，程序退出")
        sys.exit(1)
    
    # 保存WSDL文档
    client.save_wsdl()
    
    # 显示可用操作
    operations = client.get_operations()
    if operations:
        print("\n可用的操作:")
        for i, op in enumerate(operations, 1):
            print(f"{i}. {op}")
        
        # 示例：调用第一个操作（如果存在）
        if operations:
            first_operation = operations[0]
            print(f"\n尝试调用操作: {first_operation}")
            
            # 这里可以根据具体的操作添加参数
            # 例如: parameters = {"param1": "value1", "param2": "value2"}
            result = client.call_operation(first_operation)
            
            if result:
                print("操作结果:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print("未发现任何操作")


if __name__ == "__main__":
    main()
